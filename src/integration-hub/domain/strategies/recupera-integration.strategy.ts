import { Inject, Injectable } from '@nestjs/common';
import { logger } from '@edutalent/commons-sdk';
import { IntegrationStrategy } from '@integration-hub/domain/interfaces/integration-strategy.interface';
import { IntegrationContext } from '@integration-hub/domain/entities/integration-context.entity';
import { IntegrationResult } from '@integration-hub/domain/entities/integration-result.entity';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import { BusinessBasePort } from '@integration-hub/infrastructure/ports/http/business-base.port';
import { MessageType, RoleType } from '../../../common/enums';
import { randomUUID } from 'crypto';

@Injectable()
export class RecuperaIntegrationStrategy implements IntegrationStrategy {

  constructor(
    private readonly httpService: HttpService,
    @Inject('BusinessBasePort')
    private readonly businessBaseAdapter: BusinessBasePort,
  ) {}
  private readonly strategyName = 'Recupera';

  async execute(context: IntegrationContext): Promise<IntegrationResult> {
    logger.info(`Executing ${this.strategyName} strategy`, {
      customerId: context.customerId,
      portfolioItemId: context.portfolioItemId,
      currentStatus: context.currentStatus,
      providerName: context.getProviderName(),
    });

    try {
      const portfolioItem = await this.businessBaseAdapter.getPortfolioItem(context.portfolioItemId);
      if (!portfolioItem || !portfolioItem.customData) {
        throw new Error('Portfolio item custom data not found');
      }

      const integrationData = await this.performRecuperaIntegration(context, portfolioItem.customData);
      
      logger.info(`${this.strategyName} strategy executed successfully`, {
        customerId: context.customerId,
        portfolioItemId: context.portfolioItemId,
      });

      // Convert base64 to PDF file and send to business base
      const pdfFile = this.base64ToPdfFile(integrationData);
      await this.businessBaseAdapter.sendDirectMessage(context.portfolioItemId, {
        message: 'Boleto para pagamento.',
        messageType: MessageType.PDF,
        roleType: RoleType.ASSISTANT,
      }, pdfFile);

      return IntegrationResult.success(
        this.strategyName,
        'Recupera integration completed successfully',
        {
          executionTime: new Date().toISOString(),
          portfolioItemId: context.portfolioItemId,
        }
      );
    } catch (error) {
      logger.error(`${this.strategyName} strategy execution failed`, {
        customerId: context.customerId,
        portfolioItemId: context.portfolioItemId,
        error: error.message,
      });

      throw error
    }
  }

  base64ToPdfFile(base64: string): Express.Multer.File {
    // Validate input
    if (!base64 || typeof base64 !== 'string') {
      throw new Error('Invalid base64 input: must be a non-empty string');
    }

    // Extract base64 data, handling both data URLs and raw base64
    const matches = base64.match(/^data:application\/pdf;base64,(.+)$/);
    const data = matches ? matches[1] : base64;

    // Validate base64 format
    if (!/^[A-Za-z0-9+/]*={0,2}$/.test(data)) {
      throw new Error('Invalid base64 format');
    }

    let buffer: Buffer;
    try {
      buffer = Buffer.from(data, 'base64');
    } catch (error) {
      throw new Error(`Failed to decode base64 data: ${error.message}`);
    }

    // Validate PDF header (basic validation)
    if (buffer.length < 4 || buffer.subarray(0, 4).toString() !== '%PDF') {
      throw new Error('Invalid PDF data: missing PDF header');
    }

    const filename = `${randomUUID()}.pdf`;

    // Create Express.Multer.File object optimized for HTTP requests
    // No need to write to filesystem since we're sending via HTTP
    const file: Express.Multer.File = {
      fieldname: 'file',
      originalname: filename,
      encoding: '7bit',
      mimetype: 'application/pdf',
      size: buffer.length,
      destination: '', 
      filename,
      path: '', 
      buffer, 
      stream: undefined as any,
    };

    return file;
  }

  getStrategyName(): string {
    return this.strategyName;
  }

  canHandle(context: IntegrationContext): boolean {
    const providerName = context.getProviderName();
    return providerName && providerName.toLowerCase() === this.strategyName.toLowerCase();
  }

  private async performRecuperaIntegration(context: IntegrationContext, customData: any): Promise<string> {
    try{
      
    const authToken = await this.getAuthToken(context);

    //TODO: should handle logic to know if it's a VALOR_ORIGINAL_DA_DIVIDA(primeira via) or VALOR_DIVIDA_CORRIGIDO(segunda via)
    
    const url = 'https://cobrancaapi-matrixenergia.recupera.com.br/api/boleto/original/pdf';
    const headers = {
      accept: 'text/plain',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${authToken}`,
    };
    
    const body = {
      chaveCliente: {
        codigoCredor: customData['CODIGO_DO_CREDOR'],
        codigoCliente: customData['CODIGO_DO_CLIENTE'],
      },
      dataPagamento: customData['DATA_PAGAMENTO'],
      divida: [
        {
          codigoProduto: customData['CODIGO_DO_PRODUTO'],
          codigoContrato: customData['CODIGO_DO_CONTRATO'],
          prestacoes: [
            {
              numeroPrestacao: customData['NUMERO_DA_PRESTACAO'] || '',
              dataVencimento: customData['DATA_VENCIMENTO'],
              idPrestacao: customData['ID_DA_PRESTACAO'],
            },
          ],
        },
      ],
    };

    const result = await lastValueFrom(this.httpService.post(url, body, { headers }));
    
    logger.debug(`Performing Recupera integration for portfolio item: ${context.portfolioItemId}`);
    
    return result.data.boletoPDF;
    } catch (error) {
      logger.error('Failed to perform Recupera integration', error);
      throw error;
    }
  }

  private async getAuthToken(context: IntegrationContext): Promise<string> {
   try{
    const url = 'https://cobrancaapi-matrixenergia.recupera.com.br/api/autenticacao/autenticar';
    const headers = {
      accept: 'text/plain',
      'Content-Type': 'application/json',
    };

    const body = {
      usuario: context.integrationConfig.credentials.usuario,
      senha: context.integrationConfig.credentials.senha,
      empresa: context.integrationConfig.credentials.empresa,
      chaveAtivacao: context.integrationConfig.credentials.chaveAtivacao,
    };

    const response = await lastValueFrom(this.httpService.post(url, body, { headers }));
    return response?.data?.access_token;
    } catch (error) {
      logger.error('Failed to get auth token', error);
      throw error;
    }
  }
}
