import { Inject, Injectable } from '@nestjs/common';
import { logger } from '@edutalent/commons-sdk';
import { IntegrationStrategy } from '@integration-hub/domain/interfaces/integration-strategy.interface';
import { IntegrationContext } from '@integration-hub/domain/entities/integration-context.entity';
import { IntegrationResult } from '@integration-hub/domain/entities/integration-result.entity';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom, timeout } from 'rxjs';
import { BusinessBasePort } from '@integration-hub/infrastructure/ports/http/business-base.port';
import { MessageType, RoleType } from '../../../common/enums';
import { randomUUID } from 'crypto';

interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  timeoutMs: number;
  jitterFactor: number;
}

interface AuthTokenInfo {
  token: string;
  expiresAt: Date;
}



enum ErrorType {
  RECOVERABLE = 'recoverable',
  PERMANENT = 'permanent',
  AUTH_EXPIRED = 'auth_expired'
}

@Injectable()
export class RecuperaIntegrationStrategy implements IntegrationStrategy {
  private readonly strategyName = 'Recupera';
  private authTokenInfo: AuthTokenInfo | null = null;
  private readonly authMutex = new Map<string, Promise<void>>();

  private readonly defaultRetryConfig: RetryConfig = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    timeoutMs: 30000,
    jitterFactor: 0.1
  };

  constructor(
    private readonly httpService: HttpService,
    @Inject('BusinessBasePort')
    private readonly businessBaseAdapter: BusinessBasePort,
  ) {}

  async execute(context: IntegrationContext): Promise<IntegrationResult> {
    logger.info(`Executing ${this.strategyName} strategy`, {
      customerId: context.customerId,
      portfolioItemId: context.portfolioItemId,
      currentStatus: context.currentStatus,
      providerName: context.getProviderName(),
    });

    try {
      const portfolioItem = await this.businessBaseAdapter.getPortfolioItem(context.portfolioItemId);
      if (!portfolioItem || !portfolioItem.customData) {
        throw new Error('Portfolio item custom data not found');
      }

      const integrationData = await this.performRecuperaIntegration(context, portfolioItem.customData);
      
      logger.info(`${this.strategyName} strategy executed successfully`, {
        customerId: context.customerId,
        portfolioItemId: context.portfolioItemId,
      });

      // Convert base64 to PDF file and send to business base
      const pdfFile = this.base64ToPdfFile(integrationData);
      await this.businessBaseAdapter.sendDirectMessage(context.portfolioItemId, {
        message: 'Boleto para pagamento.',
        messageType: MessageType.PDF,
        roleType: RoleType.ASSISTANT,
      }, pdfFile);

      return IntegrationResult.success(
        this.strategyName,
        'Recupera integration completed successfully',
        {
          executionTime: new Date().toISOString(),
          portfolioItemId: context.portfolioItemId,
        }
      );
    } catch (error) {
      logger.error(`${this.strategyName} strategy execution failed`, {
        customerId: context.customerId,
        portfolioItemId: context.portfolioItemId,
        error: error.message,
      });

      throw error
    }
  }

  /**
   * Converte base64 para arquivo PDF (versão simplificada)
   */
  base64ToPdfFile(base64: string): Express.Multer.File {
    if (!base64 || typeof base64 !== 'string') {
      throw new Error('Invalid base64 input: must be a non-empty string');
    }

    // Remove data URL prefix se presente
    const data = base64.replace(/^data:application\/pdf;base64,/, '');

    let buffer: Buffer;
    try {
      buffer = Buffer.from(data, 'base64');
    } catch (error) {
      throw new Error(`Failed to decode base64 data: ${error.message}`);
    }

    // Validação básica de header PDF
    if (buffer.length < 4 || !buffer.subarray(0, 4).toString().startsWith('%PDF')) {
      throw new Error('Invalid PDF data: missing PDF header');
    }

    const filename = `${randomUUID()}.pdf`;

    return {
      fieldname: 'file',
      originalname: filename,
      encoding: '7bit',
      mimetype: 'application/pdf',
      size: buffer.length,
      destination: '',
      filename,
      path: '',
      buffer,
      stream: undefined as any,
    };
  }

  getStrategyName(): string {
    return this.strategyName;
  }

  canHandle(context: IntegrationContext): boolean {
    const providerName = context.getProviderName();
    return providerName && providerName.toLowerCase() === this.strategyName.toLowerCase();
  }

  /**
   * Classifica o tipo de erro para determinar se deve ser reprocessado
   */
  private classifyError(error: any): ErrorType {
    if (!error.response) {
      // Erros de rede, timeout, etc.
      return ErrorType.RECOVERABLE;
    }

    const status = error.response.status;
    const authHeader = error.response.headers?.['www-authenticate'];

    // Token expirado/inválido
    if (status === 401 && authHeader?.includes('invalid_token')) {
      return ErrorType.AUTH_EXPIRED;
    }

    // Erros recuperáveis: 5xx, 429, alguns 4xx específicos
    if (status >= 500 || status === 429 || status === 408 || status === 502 || status === 503 || status === 504) {
      return ErrorType.RECOVERABLE;
    }

    // Erros permanentes: 400, 404, 403, etc.
    return ErrorType.PERMANENT;
  }

  /**
   * Calcula delay com jitter aleatório para evitar thundering herd
   */
  private calculateDelayWithJitter(baseDelay: number, attempt: number, config: RetryConfig): number {
    const exponentialDelay = Math.min(baseDelay * Math.pow(2, attempt), config.maxDelay);
    const jitter = exponentialDelay * config.jitterFactor * Math.random();
    return Math.floor(exponentialDelay + jitter);
  }

  /**
   * Sistema de retry robusto com controle de timeout global
   */
  private async executeWithRetry<T>(
    operation: () => Promise<T>,
    operationType: string,
    context: IntegrationContext,
    config: RetryConfig = this.defaultRetryConfig
  ): Promise<T> {
    const startTime = Date.now();
    let lastError: any;

    for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
      // Verifica timeout global
      if (Date.now() - startTime > config.timeoutMs) {
        const timeoutError = new Error(`Operation ${operationType} timed out after ${config.timeoutMs}ms`);
        logger.error('Operation timed out', {
          operationType,
          portfolioItemId: context.portfolioItemId,
          customerId: context.customerId,
          totalTime: Date.now() - startTime,
          attempts: attempt + 1
        });
        throw timeoutError;
      }

      try {
        const result = await Promise.race([
          operation(),
          new Promise<never>((_, reject) =>
            setTimeout(() => reject(new Error(`Request timeout after ${config.timeoutMs}ms`)), config.timeoutMs)
          )
        ]);

        if (attempt > 0) {
          logger.info('Operation succeeded after retry', {
            operationType,
            portfolioItemId: context.portfolioItemId,
            customerId: context.customerId,
            attempts: attempt + 1,
            totalTime: Date.now() - startTime
          });
        }

        return result;
      } catch (error) {
        lastError = error;
        const errorType = this.classifyError(error);

        logger.warn('Operation failed', {
          operationType,
          portfolioItemId: context.portfolioItemId,
          customerId: context.customerId,
          attempt: attempt + 1,
          maxRetries: config.maxRetries + 1,
          errorType,
          errorMessage: error.message,
          statusCode: error.response?.status
        });

        // Se é erro de autenticação, tenta renovar token
        if (errorType === ErrorType.AUTH_EXPIRED) {
          try {
            await this.refreshAuthToken(context);
            continue; // Tenta novamente com novo token
          } catch (authError) {
            logger.error('Failed to refresh auth token', {
              portfolioItemId: context.portfolioItemId,
              customerId: context.customerId,
              authError: authError.message
            });
            throw authError;
          }
        }

        // Se é erro permanente, não tenta novamente
        if (errorType === ErrorType.PERMANENT) {
          logger.error('Permanent error, not retrying', {
            operationType,
            portfolioItemId: context.portfolioItemId,
            customerId: context.customerId,
            errorMessage: error.message,
            statusCode: error.response?.status
          });
          throw error;
        }

        // Se chegou ao limite de tentativas
        if (attempt >= config.maxRetries) {
          logger.error('Max retries exceeded', {
            operationType,
            portfolioItemId: context.portfolioItemId,
            customerId: context.customerId,
            attempts: attempt + 1,
            totalTime: Date.now() - startTime,
            finalError: error.message
          });
          throw error;
        }

        // Aguarda antes da próxima tentativa
        const delay = this.calculateDelayWithJitter(config.baseDelay, attempt, config);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError;
  }

  /**
   * Verifica se o token está válido (não expirado)
   */
  private isTokenValid(): boolean {
    return this.authTokenInfo !== null &&
           this.authTokenInfo.expiresAt > new Date();
  }

  /**
   * Obtém token com proteção contra race conditions
   */
  private async ensureValidToken(context: IntegrationContext): Promise<string> {
    // Se token ainda é válido, retorna imediatamente
    if (this.isTokenValid()) {
      return this.authTokenInfo!.token;
    }

    const mutexKey = `${context.customerId}-${context.integrationConfig.credentials.usuario}`;

    // Se já existe uma operação de renovação em andamento, aguarda
    if (this.authMutex.has(mutexKey)) {
      await this.authMutex.get(mutexKey);
      if (this.isTokenValid()) {
        return this.authTokenInfo!.token;
      }
    }

    // Cria nova operação de renovação
    const renewalPromise = this.renewToken(context);
    this.authMutex.set(mutexKey, renewalPromise);

    try {
      await renewalPromise;
      return this.authTokenInfo!.token;
    } finally {
      this.authMutex.delete(mutexKey);
    }
  }

  /**
   * Renova o token de autenticação
   */
  private async renewToken(context: IntegrationContext): Promise<void> {
    const url = 'https://cobrancaapi-matrixenergia.recupera.com.br/api/autenticacao/autenticar';
    const headers = {
      accept: 'text/plain',
      'Content-Type': 'application/json',
    };

    const body = {
      usuario: context.integrationConfig.credentials.usuario,
      senha: context.integrationConfig.credentials.senha,
      empresa: context.integrationConfig.credentials.empresa,
      chaveAtivacao: context.integrationConfig.credentials.chaveAtivacao,
    };

    try {
      const response = await lastValueFrom(
        this.httpService.post(url, body, {
          headers,
          timeout: this.defaultRetryConfig.timeoutMs
        }).pipe(timeout(this.defaultRetryConfig.timeoutMs))
      );

      // Assume que o token expira em 1 hora (padrão JWT)
      const expiresAt = new Date(Date.now() + 55 * 60 * 1000); // 55 minutos para margem de segurança

      this.authTokenInfo = {
        token: response.data.access_token,
        expiresAt
      };

      logger.info('Auth token renewed successfully', {
        customerId: context.customerId,
        portfolioItemId: context.portfolioItemId,
        expiresAt: expiresAt.toISOString()
      });
    } catch (error) {
      logger.error('Failed to renew auth token', {
        customerId: context.customerId,
        portfolioItemId: context.portfolioItemId,
        errorMessage: error.message,
        statusCode: error.response?.status
      });
      throw new Error(`Authentication failed: ${error.message}`);
    }
  }

  /**
   * Força renovação do token (usado quando detectado token expirado)
   */
  private async refreshAuthToken(context: IntegrationContext): Promise<void> {
    this.authTokenInfo = null; // Invalida token atual
    await this.ensureValidToken(context);
  }

  private async performRecuperaIntegration(context: IntegrationContext, customData: any): Promise<string> {
    try {
      const pdfBase64 = await this.getBankSlipPdfBase64(context, customData, 'duplicate');
      return pdfBase64;
    } catch (error) {
      logger.error('Failed to perform recupera integration', {
        customerId: context.customerId,
        portfolioItemId: context.portfolioItemId,
        errorMessage: error.message
      });
      throw error;
    }
  }

  /**
   * Constrói o corpo da requisição para API do Recupera
   */
  private buildRequestBody(customData: any): any {
    return {
      chaveCliente: {
        codigoCredor: customData['CODIGO_DO_CREDOR'],
        codigoCliente: customData['CODIGO_DO_CLIENTE'],
      },
      dataPagamento: customData['DATA_PAGAMENTO'],
      divida: [
        {
          codigoProduto: customData['CODIGO_DO_PRODUTO'],
          codigoContrato: customData['CODIGO_DO_CONTRATO'],
          prestacoes: [
            {
              numeroPrestacao: customData['NUMERO_DA_PRESTACAO'] || '',
              dataVencimento: customData['DATA_VENCIMENTO'],
              idPrestacao: customData['ID_DA_PRESTACAO'],
            },
          ],
        },
      ],
    };
  }

  /**
   * Método genérico para chamadas à API do Recupera
   */
  private async makeRecuperaApiCall(
    context: IntegrationContext,
    customData: any,
    endpoint: string,
    operationType: string
  ): Promise<string> {
    const operation = async (): Promise<string> => {
      const token = await this.ensureValidToken(context);

      const url = `https://cobrancaapi-matrixenergia.recupera.com.br/api/boleto/${endpoint}`;
      const headers = {
        accept: 'text/plain',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      };

      const body = this.buildRequestBody(customData);

      const result = await lastValueFrom(
        this.httpService.post(url, body, {
          headers,
          timeout: this.defaultRetryConfig.timeoutMs
        }).pipe(timeout(this.defaultRetryConfig.timeoutMs))
      );

      return result.data.boletoPDF;
    };

    return this.executeWithRetry(operation, operationType, context);
  }

  /**
   * Obtém PDF do boleto (original ou segunda via)
   */
  private async getBankSlipPdfBase64(
    context: IntegrationContext,
    customData: any,
    type: 'original' | 'duplicate'
  ): Promise<string> {
    const endpoint = type === 'original'
      ? 'original/pdf'
      : 'segunda-via/original/pdf';

    const operationType = `get_${type}_bank_slip`;

    try {
      return await this.makeRecuperaApiCall(context, customData, endpoint, operationType);
    } catch (error) {
      // Se é segunda via e não encontrou, tenta original
      if (type === 'duplicate' &&
          error.response?.status === 400 &&
          error.response?.data?.mensagemRetorno?.includes('Dívida de segunda via original legado não foi encontrada')) {

        logger.info('Duplicate bank slip not found, trying original', {
          customerId: context.customerId,
          portfolioItemId: context.portfolioItemId,
        });

        return this.getBankSlipPdfBase64(context, customData, 'original');
      }

      throw error;
    }
  }


}
