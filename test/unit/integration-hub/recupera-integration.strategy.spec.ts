import { Test, TestingModule } from '@nestjs/testing';
import { HttpService } from '@nestjs/axios';
import { of, throwError } from 'rxjs';
import { RecuperaIntegrationStrategy } from '@integration-hub/domain/strategies/recupera-integration.strategy';
import { IntegrationContext } from '@integration-hub/domain/entities/integration-context.entity';
import { BusinessBasePort } from '@integration-hub/infrastructure/ports/http/business-base.port';
import { logger } from '@edutalent/commons-sdk';

// Mock do logger
jest.mock('@edutalent/commons-sdk', () => ({
  logger: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  },
}));

describe('RecuperaIntegrationStrategy', () => {
  let strategy: RecuperaIntegrationStrategy;
  let httpService: jest.Mocked<HttpService>;
  let businessBaseAdapter: jest.Mocked<BusinessBasePort>;

  const mockContext: IntegrationContext = {
    customerId: 'customer-123',
    portfolioItemId: 'portfolio-456',
    currentStatus: 'active',
    integrationConfig: {
      credentials: {
        usuario: 'test-user',
        senha: 'test-password',
        empresa: 'test-company',
        chaveAtivacao: 'test-key',
      },
    },
    getProviderName: () => 'Recupera',
  } as any;

  const mockCustomData = {
    CODIGO_DO_CREDOR: '123',
    CODIGO_DO_CLIENTE: '456',
    DATA_PAGAMENTO: '2024-01-01',
    CODIGO_DO_PRODUTO: '789',
    CODIGO_DO_CONTRATO: '101112',
    NUMERO_DA_PRESTACAO: '1',
    DATA_VENCIMENTO: '2024-02-01',
    ID_DA_PRESTACAO: '131415',
  };

  const mockPortfolioItem = {
    customData: mockCustomData,
  };

  const mockAuthResponse = {
    data: {
      access_token: 'mock-token-123',
    },
  };

  const mockBankSlipResponse = {
    data: {
      boletoPDF: 'JVBERi0xLjQKJcOkw7zDtsO8CjIgMCBvYmoKPDwKL0xlbmd0aCAzIDAgUgo+PgpzdHJlYW0KQNP=', // Mock base64 PDF
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RecuperaIntegrationStrategy,
        {
          provide: HttpService,
          useValue: {
            post: jest.fn(),
          },
        },
        {
          provide: 'BusinessBasePort',
          useValue: {
            getPortfolioItem: jest.fn(),
            sendDirectMessage: jest.fn(),
          },
        },
      ],
    }).compile();

    strategy = module.get<RecuperaIntegrationStrategy>(RecuperaIntegrationStrategy);
    httpService = module.get(HttpService);
    businessBaseAdapter = module.get('BusinessBasePort');

    // Reset mocks
    jest.clearAllMocks();
  });

  describe('execute', () => {
    it('should execute successfully with valid data', async () => {
      // Arrange
      businessBaseAdapter.getPortfolioItem.mockResolvedValue(mockPortfolioItem);
      httpService.post
        .mockReturnValueOnce(of(mockAuthResponse)) // Auth call
        .mockReturnValueOnce(of(mockBankSlipResponse)); // Bank slip call
      businessBaseAdapter.sendDirectMessage.mockResolvedValue(undefined);

      // Act
      const result = await strategy.execute(mockContext);

      // Assert
      expect(result.success).toBe(true);
      expect(result.strategyName).toBe('Recupera');
      expect(businessBaseAdapter.getPortfolioItem).toHaveBeenCalledWith(mockContext.portfolioItemId);
      expect(businessBaseAdapter.sendDirectMessage).toHaveBeenCalled();
    });

    it('should throw error when portfolio item not found', async () => {
      // Arrange
      businessBaseAdapter.getPortfolioItem.mockResolvedValue(null);

      // Act & Assert
      await expect(strategy.execute(mockContext)).rejects.toThrow('Portfolio item custom data not found');
    });

    it('should throw error when custom data is missing', async () => {
      // Arrange
      businessBaseAdapter.getPortfolioItem.mockResolvedValue({ customData: null });

      // Act & Assert
      await expect(strategy.execute(mockContext)).rejects.toThrow('Portfolio item custom data not found');
    });
  });

  describe('canHandle', () => {
    it('should return true for Recupera provider', () => {
      expect(strategy.canHandle(mockContext)).toBe(true);
    });

    it('should return false for other providers', () => {
      const otherContext = {
        ...mockContext,
        getProviderName: () => 'OtherProvider',
      } as any;

      expect(strategy.canHandle(otherContext)).toBe(false);
    });

    it('should be case insensitive', () => {
      const lowerCaseContext = {
        ...mockContext,
        getProviderName: () => 'recupera',
      } as any;

      expect(strategy.canHandle(lowerCaseContext)).toBe(true);
    });
  });

  describe('getStrategyName', () => {
    it('should return correct strategy name', () => {
      expect(strategy.getStrategyName()).toBe('Recupera');
    });
  });

  describe('base64ToPdfFile', () => {
    const validBase64 = 'JVBERi0xLjQKJcOkw7zDtsO4CjIgMCBvYmoKPDwKL0xlbmd0aCAzIDAgUgo+PgpzdHJlYW0KQNP=';

    it('should convert valid base64 to PDF file', () => {
      const result = strategy.base64ToPdfFile(validBase64);

      expect(result.mimetype).toBe('application/pdf');
      expect(result.buffer).toBeInstanceOf(Buffer);
      expect(result.filename).toMatch(/\.pdf$/);
      expect(result.size).toBeGreaterThan(0);
    });

    it('should handle data URL format', () => {
      const dataUrl = `data:application/pdf;base64,${validBase64}`;
      const result = strategy.base64ToPdfFile(dataUrl);

      expect(result.mimetype).toBe('application/pdf');
      expect(result.buffer).toBeInstanceOf(Buffer);
    });

    it('should throw error for invalid input', () => {
      expect(() => strategy.base64ToPdfFile('')).toThrow('Invalid base64 input');
      expect(() => strategy.base64ToPdfFile(null as any)).toThrow('Invalid base64 input');
      expect(() => strategy.base64ToPdfFile(123 as any)).toThrow('Invalid base64 input');
    });

    it('should throw error for invalid base64', () => {
      expect(() => strategy.base64ToPdfFile('invalid-base64')).toThrow('Failed to decode base64 data');
    });

    it('should throw error for non-PDF data', () => {
      const nonPdfBase64 = Buffer.from('not a pdf').toString('base64');
      expect(() => strategy.base64ToPdfFile(nonPdfBase64)).toThrow('Invalid PDF data: missing PDF header');
    });
  });

  describe('Authentication and Token Management', () => {
    beforeEach(() => {
      // Reset internal token state
      (strategy as any).authTokenInfo = null;
    });

    it('should authenticate and cache token', async () => {
      // Arrange
      businessBaseAdapter.getPortfolioItem.mockResolvedValue(mockPortfolioItem);
      httpService.post
        .mockReturnValueOnce(of(mockAuthResponse)) // Auth call
        .mockReturnValueOnce(of(mockBankSlipResponse)); // Bank slip call
      businessBaseAdapter.sendDirectMessage.mockResolvedValue(undefined);

      // Act
      await strategy.execute(mockContext);

      // Assert
      expect(httpService.post).toHaveBeenCalledWith(
        'https://cobrancaapi-matrixenergia.recupera.com.br/api/autenticacao/autenticar',
        expect.objectContaining({
          usuario: mockContext.integrationConfig.credentials.usuario,
          senha: mockContext.integrationConfig.credentials.senha,
        }),
        expect.any(Object)
      );

      // Verify token is cached
      const tokenInfo = (strategy as any).authTokenInfo;
      expect(tokenInfo).toBeTruthy();
      expect(tokenInfo.token).toBe('mock-token-123');
      expect(tokenInfo.expiresAt).toBeInstanceOf(Date);
    });

    it('should reuse valid cached token', async () => {
      // Arrange - Set up cached token
      const futureDate = new Date(Date.now() + 60 * 60 * 1000); // 1 hour from now
      (strategy as any).authTokenInfo = {
        token: 'cached-token',
        expiresAt: futureDate,
      };

      businessBaseAdapter.getPortfolioItem.mockResolvedValue(mockPortfolioItem);
      httpService.post.mockReturnValueOnce(of(mockBankSlipResponse)); // Only bank slip call
      businessBaseAdapter.sendDirectMessage.mockResolvedValue(undefined);

      // Act
      await strategy.execute(mockContext);

      // Assert - Should not call auth endpoint
      expect(httpService.post).toHaveBeenCalledTimes(1);
      expect(httpService.post).toHaveBeenCalledWith(
        expect.stringContaining('boleto'),
        expect.any(Object),
        expect.objectContaining({
          headers: expect.objectContaining({
            Authorization: 'Bearer cached-token',
          }),
        })
      );
    });

    it('should refresh expired token', async () => {
      // Arrange - Set up expired token
      const pastDate = new Date(Date.now() - 60 * 60 * 1000); // 1 hour ago
      (strategy as any).authTokenInfo = {
        token: 'expired-token',
        expiresAt: pastDate,
      };

      businessBaseAdapter.getPortfolioItem.mockResolvedValue(mockPortfolioItem);
      httpService.post
        .mockReturnValueOnce(of(mockAuthResponse)) // New auth call
        .mockReturnValueOnce(of(mockBankSlipResponse)); // Bank slip call
      businessBaseAdapter.sendDirectMessage.mockResolvedValue(undefined);

      // Act
      await strategy.execute(mockContext);

      // Assert - Should call auth endpoint to refresh
      expect(httpService.post).toHaveBeenCalledTimes(2);
      expect(httpService.post).toHaveBeenNthCalledWith(
        1,
        'https://cobrancaapi-matrixenergia.recupera.com.br/api/autenticacao/autenticar',
        expect.any(Object),
        expect.any(Object)
      );
    });
  });

  describe('Retry Logic and Error Handling', () => {
    beforeEach(() => {
      (strategy as any).authTokenInfo = null;
    });

    it('should retry on network errors', async () => {
      // Arrange
      businessBaseAdapter.getPortfolioItem.mockResolvedValue(mockPortfolioItem);
      httpService.post
        .mockReturnValueOnce(of(mockAuthResponse)) // Auth call
        .mockReturnValueOnce(throwError(() => new Error('Network error'))) // First bank slip call fails
        .mockReturnValueOnce(of(mockBankSlipResponse)); // Second bank slip call succeeds
      businessBaseAdapter.sendDirectMessage.mockResolvedValue(undefined);

      // Act
      const result = await strategy.execute(mockContext);

      // Assert
      expect(result.success).toBe(true);
      expect(httpService.post).toHaveBeenCalledTimes(3); // Auth + 2 bank slip attempts
    });

    it('should handle 401 errors by refreshing token', async () => {
      // Arrange
      const authError = {
        response: {
          status: 401,
          headers: { 'www-authenticate': 'Bearer error="invalid_token"' },
        },
      };

      businessBaseAdapter.getPortfolioItem.mockResolvedValue(mockPortfolioItem);
      httpService.post
        .mockReturnValueOnce(of(mockAuthResponse)) // Initial auth call
        .mockReturnValueOnce(throwError(() => authError)) // Bank slip call fails with 401
        .mockReturnValueOnce(of(mockAuthResponse)) // Token refresh call
        .mockReturnValueOnce(of(mockBankSlipResponse)); // Retry bank slip call succeeds
      businessBaseAdapter.sendDirectMessage.mockResolvedValue(undefined);

      // Act
      const result = await strategy.execute(mockContext);

      // Assert
      expect(result.success).toBe(true);
      expect(httpService.post).toHaveBeenCalledTimes(4);
    });

    it('should not retry on permanent errors (400)', async () => {
      // Arrange
      const permanentError = {
        response: {
          status: 400,
          data: { mensagemRetorno: 'Invalid request' },
        },
      };

      businessBaseAdapter.getPortfolioItem.mockResolvedValue(mockPortfolioItem);
      httpService.post
        .mockReturnValueOnce(of(mockAuthResponse)) // Auth call
        .mockReturnValueOnce(throwError(() => permanentError)); // Bank slip call fails permanently

      // Act & Assert
      await expect(strategy.execute(mockContext)).rejects.toMatchObject({
        response: { status: 400 },
      });

      expect(httpService.post).toHaveBeenCalledTimes(2); // Auth + 1 bank slip attempt (no retry)
    });

    it('should fallback to original bank slip when duplicate not found', async () => {
      // Arrange
      const duplicateNotFoundError = {
        response: {
          status: 400,
          data: { mensagemRetorno: 'Dívida de segunda via original legado não foi encontrada' },
        },
      };

      businessBaseAdapter.getPortfolioItem.mockResolvedValue(mockPortfolioItem);
      httpService.post
        .mockReturnValueOnce(of(mockAuthResponse)) // Auth call
        .mockReturnValueOnce(throwError(() => duplicateNotFoundError)) // Duplicate bank slip fails
        .mockReturnValueOnce(of(mockBankSlipResponse)); // Original bank slip succeeds
      businessBaseAdapter.sendDirectMessage.mockResolvedValue(undefined);

      // Act
      const result = await strategy.execute(mockContext);

      // Assert
      expect(result.success).toBe(true);
      expect(httpService.post).toHaveBeenCalledTimes(3);

      // Verify calls to both endpoints
      expect(httpService.post).toHaveBeenCalledWith(
        expect.stringContaining('segunda-via/original/pdf'),
        expect.any(Object),
        expect.any(Object)
      );
      expect(httpService.post).toHaveBeenCalledWith(
        expect.stringContaining('original/pdf'),
        expect.any(Object),
        expect.any(Object)
      );
    });

    it('should respect timeout configuration', async () => {
      // Arrange
      businessBaseAdapter.getPortfolioItem.mockResolvedValue(mockPortfolioItem);
      httpService.post
        .mockReturnValueOnce(of(mockAuthResponse)) // Auth call
        .mockReturnValueOnce(
          new Promise((resolve) => setTimeout(() => resolve(of(mockBankSlipResponse)), 35000))
        ); // Bank slip call that takes too long

      // Act & Assert
      await expect(strategy.execute(mockContext)).rejects.toThrow(/timeout/i);
    }, 40000);
  });
});
